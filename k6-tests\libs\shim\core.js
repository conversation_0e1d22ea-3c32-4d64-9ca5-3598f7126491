// Postman to k6 core shim library
import { check, group } from 'k6';
import http from 'k6/http';

// Global postman object
global.postman = {};

// Symbol for request handling
const Request = Symbol.for('request');
const Initial = Symbol.for('initial');

// Environment variables storage
let environment = {};

// Initialize postman environment
postman[Initial] = function(config) {
  if (config.environment) {
    environment = { ...config.environment };
  }
  if (config.options) {
    // Merge k6 options
    Object.assign(global.options || {}, config.options);
  }
};

// Request handler
postman[Request] = function(requestConfig) {
  const { name, method, address, data, headers = {}, pre, post, auth } = requestConfig;
  
  group(name, function() {
    // Execute pre-request script
    if (pre && typeof pre === 'function') {
      pre();
    }
    
    // Replace environment variables in address
    let url = address;
    Object.keys(environment).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      url = url.replace(regex, environment[key]);
    });
    
    // Replace environment variables in data
    let requestData = data;
    if (requestData) {
      Object.keys(environment).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        requestData = requestData.replace(regex, environment[key]);
      });
    }
    
    // Replace environment variables in headers
    const processedHeaders = {};
    Object.keys(headers).forEach(key => {
      let value = headers[key];
      Object.keys(environment).forEach(envKey => {
        const regex = new RegExp(`{{${envKey}}}`, 'g');
        value = value.replace(regex, environment[envKey]);
      });
      processedHeaders[key] = value;
    });
    
    // Handle authentication
    if (auth && typeof auth === 'function') {
      const config = { address: url, headers: processedHeaders, options: {} };
      const Var = (key) => environment[key];
      auth(config, Var);
      url = config.address;
      Object.assign(processedHeaders, config.headers);
    }
    
    // Make HTTP request
    const params = {
      headers: processedHeaders
    };
    
    let response;
    switch (method.toUpperCase()) {
      case 'GET':
        response = http.get(url, params);
        break;
      case 'POST':
        response = http.post(url, requestData, params);
        break;
      case 'PUT':
        response = http.put(url, requestData, params);
        break;
      case 'DELETE':
        response = http.del(url, params);
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
    
    // Create pm object for post-request scripts
    global.pm = {
      response: {
        json: () => {
          try {
            return JSON.parse(response.body);
          } catch (e) {
            return {};
          }
        },
        status: response.status,
        headers: response.headers
      },
      test: (name, fn) => {
        check(response, { [name]: fn });
      },
      expect: (value) => ({
        to: {
          be: {
            true: value === true,
            false: value === false
          }
        }
      }),
      environment: {
        set: (key, value) => {
          environment[key] = value;
        },
        get: (key) => environment[key]
      }
    };
    
    // Execute post-request script
    if (post && typeof post === 'function') {
      post(response);
    }
  });
};

export { postman, environment };
