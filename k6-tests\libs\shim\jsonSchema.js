// Postman to k6 JSON Schema validation shim library
// Simplified JSON schema validation for k6

function validateSchema(data, schema) {
  if (!schema || !data) {
    return false;
  }

  // Type validation
  if (schema.type) {
    const dataType = Array.isArray(data) ? 'array' : typeof data;
    if (dataType !== schema.type) {
      return false;
    }
  }

  // Required properties validation
  if (schema.required && Array.isArray(schema.required)) {
    for (const prop of schema.required) {
      if (!(prop in data)) {
        return false;
      }
    }
  }

  // Properties validation
  if (schema.properties && typeof data === 'object' && !Array.isArray(data)) {
    for (const [prop, propSchema] of Object.entries(schema.properties)) {
      if (prop in data) {
        if (!validateSchema(data[prop], propSchema)) {
          return false;
        }
      }
    }
  }

  // Array items validation
  if (schema.items && Array.isArray(data)) {
    for (const item of data) {
      if (!validateSchema(item, schema.items)) {
        return false;
      }
    }
  }

  return true;
}

// Extend pm object with schema validation
if (typeof global.pm === 'undefined') {
  global.pm = {};
}

global.pm.validateSchema = validateSchema;

export { validateSchema };
