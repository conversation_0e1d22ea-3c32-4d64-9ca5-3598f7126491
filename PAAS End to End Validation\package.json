﻿{
  "name": "paas-validation",
  "version": "1.0.0",
  "description": "PAAS End-to-End Validation with <PERSON><PERSON> and k6 Performance Testing",
  "main": "index.js",
  "scripts": {
    "test": "playwright test",
    "test:headed": "playwright test --headed",
    "test:ui": "playwright test --ui",
    "k6:smoke": "k6 run --config ../k6-tests/configs/smoke-test.json ../k6-tests/scripts/paaswallet.js",
    "k6:load": "k6 run --config ../k6-tests/configs/load-test.json ../k6-tests/scripts/paaswallet.js",
    "k6:stress": "k6 run --config ../k6-tests/configs/stress-test.json ../k6-tests/scripts/paaswallet.js",
    "k6:qa": "k6 run --env-file ../k6-tests/environments/qa.env ../k6-tests/scripts/paaswallet.js",
    "k6:wallet": "k6 run ../k6-tests/scripts/paaswallet.js",
    "test:all": "npm run test && npm run k6:smoke",
    "report": "playwright show-report"
  },
  "keywords": [
    "playwright",
    "k6",
    "performance",
    "testing",
    "paas",
    "validation"
  ],
  "author": "",
  "license": "ISC",
  "devDependencies": {
    "@playwright/test": "^1.54.1",
    "@types/node": "^24.1.0"
  },
  "dependencies": {
    "playwright-extra": "^4.3.6",
    "playwright-extra-plugin-stealth": "^0.0.1"
  }
}
