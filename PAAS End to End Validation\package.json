﻿{
  "name": "paas-validation",
  "version": "1.0.0",
  "description": "PAAS End-to-End Validation with <PERSON><PERSON> and k6 Performance Testing",
  "main": "index.js",
  "scripts": {
    "test": "playwright test",
    "test:headed": "playwright test --headed",
    "test:ui": "playwright test --ui",
    "test:mparticle": "playwright test tests/mparticle-validation.spec.ts",
    "test:mparticle:headed": "playwright test tests/mparticle-validation.spec.ts --headed",
    "k6:smoke": "k6 run --config ../k6-tests/configs/smoke-test.json ../k6-tests/scripts/paaswallet.js",
    "k6:load": "k6 run --config ../k6-tests/configs/load-test.json ../k6-tests/scripts/paaswallet.js",
    "k6:stress": "k6 run --config ../k6-tests/configs/stress-test.json ../k6-tests/scripts/paaswallet.js",
    "k6:qa": "k6 run --env-file ../k6-tests/environments/qa.env ../k6-tests/scripts/paaswallet.js",
    "k6:wallet": "k6 run ../k6-tests/scripts/paaswallet.js",
    "k6:generate-data": "k6 run ../k6-tests/scripts/paaswallet.js 2>&1 | node ../scripts/extract-test-data.js -",
    "k6:generate-data:qa": "k6 run --env-file ../k6-tests/environments/qa.env ../k6-tests/scripts/paaswallet.js 2>&1 | node ../scripts/extract-test-data.js -",
    "test:e2e": "npm run k6:generate-data && npm run test:mparticle",
    "test:e2e:qa": "npm run k6:generate-data:qa && npm run test:mparticle",
    "test:all": "npm run test && npm run k6:smoke",
    "report": "playwright show-report",
    "clean:test-data": "rm -rf ../shared-test-data/*.json || del /q ..\\shared-test-data\\*.json"
  },
  "keywords": [
    "playwright",
    "k6",
    "performance",
    "testing",
    "paas",
    "validation"
  ],
  "author": "",
  "license": "ISC",
  "devDependencies": {
    "@playwright/test": "^1.54.1",
    "@types/node": "^24.1.0"
  },
  "dependencies": {
    "playwright-extra": "^4.3.6",
    "playwright-extra-plugin-stealth": "^0.0.1"
  }
}
