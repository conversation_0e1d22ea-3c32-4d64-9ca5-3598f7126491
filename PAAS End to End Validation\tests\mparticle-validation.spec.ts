import { test, expect, Page } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

// Test data interface
interface TestData {
  testRun: {
    id: string;
    timestamp: string;
    environment: string;
  };
  authentication: {
    dgpToken: string;
    loginToken: string;
    tokenExpiry?: string;
  };
  userSession: {
    userId: string;
    userName: string;
    email: string;
    registrationState: string;
  };
  wallet: {
    initialBalance?: any;
    everiWalletBalance?: any;
    finalBalance?: any;
    transactions: Array<{
      type: string;
      amount: number;
      currency: string;
      timestamp: string;
      transactionId: string;
      status: string;
    }>;
  };
  gaming: {
    gameToken?: string;
    gameSessionId?: string;
    gameUid?: string;
    rounds: Array<{
      roundId: string;
      paymentId: string;
      wagerAmount: number;
      winAmount: number;
      jackpotContribution: number;
      timestamp: string;
    }>;
  };
  policies: Array<{
    type: string;
    version: string;
    accepted: boolean;
    timestamp: string;
  }>;
  mParticleEvents: Array<{
    eventType: string;
    eventName: string;
    customAttributes: any;
    timestamp: string;
    userId: string;
  }>;
}

// Helper function to load test data
function loadTestData(): TestData | null {
  const testDataPath = path.join(__dirname, '../../shared-test-data/latest-test-data.json');
  
  if (!fs.existsSync(testDataPath)) {
    console.log('⚠️ No test data found. Run k6 tests first to generate test data.');
    return null;
  }
  
  try {
    const testDataContent = fs.readFileSync(testDataPath, 'utf8');
    return JSON.parse(testDataContent);
  } catch (error) {
    console.error('❌ Failed to load test data:', error);
    return null;
  }
}

// Helper function to wait for mParticle events
async function waitForMParticleEvent(page: Page, eventName: string, timeout = 30000) {
  return page.waitForFunction(
    (eventName) => {
      // Check if mParticle is loaded and has events
      if (window.mParticle && window.mParticle.getInstance) {
        const instance = window.mParticle.getInstance();
        if (instance && instance._Store && instance._Store._batches) {
          const batches = instance._Store._batches;
          for (const batch of batches) {
            if (batch.events) {
              for (const event of batch.events) {
                if (event.event_name === eventName || 
                    (event.custom_attributes && event.custom_attributes.event_name === eventName)) {
                  return true;
                }
              }
            }
          }
        }
      }
      return false;
    },
    eventName,
    { timeout }
  );
}

test.describe('mParticle Event Validation', () => {
  let testData: TestData | null;

  test.beforeAll(async () => {
    testData = loadTestData();
  });

  test('should have test data available', async () => {
    expect(testData).not.toBeNull();
    expect(testData?.testRun.id).toBeTruthy();
    console.log(`📊 Validating test run: ${testData?.testRun.id}`);
    console.log(`👤 User: ${testData?.userSession.userName} (${testData?.userSession.email})`);
    console.log(`🎯 Expected mParticle events: ${testData?.mParticleEvents.length}`);
  });

  test('should validate authentication events in mParticle', async ({ page }) => {
    if (!testData) {
      test.skip('No test data available');
      return;
    }

    // Navigate to mParticle Live Stream
    await page.goto('https://app.us2.mparticle.com/data-platform/livestream');
    
    // Login to mParticle (you may need to implement this based on your setup)
    await page.getByRole('textbox', { name: '<EMAIL>' }).fill('<EMAIL>');
    await page.getByRole('textbox', { name: 'your password' }).fill('Mparticle@12345');
    await page.getByRole('button', { name: 'Log In' }).click();
    
    // Wait for login and navigate to Live Stream
    await page.waitForTimeout(10000);
    await page.locator('img').click();
    await page.getByText('Everi Base App').click();
    await page.waitForTimeout(5000);
    await page.getByTestId('icon-dataPlatform-light').locator('path').click();
    await page.getByRole('link', { name: 'Live Stream' }).click();
    
    // Filter for the test user
    await page.getByRole('button', { name: 'All Inputs' }).click();
    await page.getByRole('button', { name: 'Vi - QA' }).click();
    await page.getByRole('button', { name: 'All Outputs' }).click();
    await page.getByRole('button', { name: 'Analytics - Everi Base App' }).click();

    // Validate authentication events
    const authEvents = testData.mParticleEvents.filter(event => 
      event.eventName.includes('Auth') || event.eventName.includes('Login')
    );

    for (const expectedEvent of authEvents) {
      console.log(`🔍 Looking for event: ${expectedEvent.eventName}`);
      
      // Look for the event in the live stream
      const eventLocator = page.locator(`text=${expectedEvent.eventName}`).first();
      await expect(eventLocator).toBeVisible({ timeout: 30000 });
      
      console.log(`✅ Found event: ${expectedEvent.eventName}`);
    }
  });

  test('should validate wallet transaction events in mParticle', async ({ page }) => {
    if (!testData) {
      test.skip('No test data available');
      return;
    }

    // Navigate to mParticle (assuming already logged in from previous test)
    await page.goto('https://app.us2.mparticle.com/data-platform/livestream');

    // Validate wallet-related events
    const walletEvents = testData.mParticleEvents.filter(event => 
      event.eventName.includes('Wallet') || 
      event.eventName.includes('Deposit') || 
      event.eventName.includes('Balance')
    );

    for (const expectedEvent of walletEvents) {
      console.log(`🔍 Looking for wallet event: ${expectedEvent.eventName}`);
      
      // Search for the event
      const searchBox = page.locator('input[placeholder*="Search"]').first();
      if (await searchBox.isVisible()) {
        await searchBox.fill(expectedEvent.eventName);
        await page.keyboard.press('Enter');
        await page.waitForTimeout(2000);
      }
      
      // Verify event appears in results
      const eventLocator = page.locator(`text=${expectedEvent.eventName}`).first();
      await expect(eventLocator).toBeVisible({ timeout: 30000 });
      
      // Validate event attributes if needed
      if (expectedEvent.customAttributes) {
        await eventLocator.click();
        
        // Check for specific attributes
        for (const [key, value] of Object.entries(expectedEvent.customAttributes)) {
          if (typeof value === 'string' || typeof value === 'number') {
            const attributeLocator = page.locator(`text=${key}`);
            await expect(attributeLocator).toBeVisible({ timeout: 10000 });
          }
        }
      }
      
      console.log(`✅ Validated wallet event: ${expectedEvent.eventName}`);
    }
  });

  test('should validate gaming events in mParticle', async ({ page }) => {
    if (!testData) {
      test.skip('No test data available');
      return;
    }

    // Navigate to mParticle
    await page.goto('https://app.us2.mparticle.com/data-platform/livestream');

    // Validate gaming-related events
    const gamingEvents = testData.mParticleEvents.filter(event => 
      event.eventName.includes('Game') || 
      event.eventName.includes('Wager') || 
      event.eventName.includes('Session')
    );

    for (const expectedEvent of gamingEvents) {
      console.log(`🔍 Looking for gaming event: ${expectedEvent.eventName}`);
      
      // Search for the event
      const searchBox = page.locator('input[placeholder*="Search"]').first();
      if (await searchBox.isVisible()) {
        await searchBox.fill(expectedEvent.eventName);
        await page.keyboard.press('Enter');
        await page.waitForTimeout(2000);
      }
      
      // Verify event appears
      const eventLocator = page.locator(`text=${expectedEvent.eventName}`).first();
      await expect(eventLocator).toBeVisible({ timeout: 30000 });
      
      console.log(`✅ Validated gaming event: ${expectedEvent.eventName}`);
    }
  });

  test('should validate user session events', async ({ page }) => {
    if (!testData) {
      test.skip('No test data available');
      return;
    }

    // Navigate to mParticle
    await page.goto('https://app.us2.mparticle.com/data-platform/livestream');

    // Validate session events (sessionStart, sessionEnd)
    const sessionEvents = testData.mParticleEvents.filter(event => 
      event.eventType === 'sessionStart' || event.eventType === 'sessionEnd'
    );

    for (const expectedEvent of sessionEvents) {
      console.log(`🔍 Looking for session event: ${expectedEvent.eventType}`);
      
      // Look for session events in the stream
      const eventLocator = page.locator(`text=${expectedEvent.eventType}`).first();
      await expect(eventLocator).toBeVisible({ timeout: 30000 });
      
      console.log(`✅ Validated session event: ${expectedEvent.eventType}`);
    }
  });

  test('should validate event timing and sequence', async ({ page }) => {
    if (!testData) {
      test.skip('No test data available');
      return;
    }

    console.log('🕐 Validating event timing and sequence...');
    
    // Check that events occurred in the expected order
    const sortedEvents = testData.mParticleEvents.sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // Validate that login events come before gaming events
    const loginEventIndex = sortedEvents.findIndex(e => e.eventName.includes('Login'));
    const gameEventIndex = sortedEvents.findIndex(e => e.eventName.includes('Game'));
    
    if (loginEventIndex >= 0 && gameEventIndex >= 0) {
      expect(loginEventIndex).toBeLessThan(gameEventIndex);
      console.log('✅ Event sequence validation passed: Login before Gaming');
    }

    // Validate that deposit events come before wager events
    const depositEventIndex = sortedEvents.findIndex(e => e.eventName.includes('Deposit'));
    const wagerEventIndex = sortedEvents.findIndex(e => e.eventName.includes('Wager'));
    
    if (depositEventIndex >= 0 && wagerEventIndex >= 0) {
      expect(depositEventIndex).toBeLessThan(wagerEventIndex);
      console.log('✅ Event sequence validation passed: Deposit before Wager');
    }
  });
});
