#!/usr/bin/env node

/**
 * Test Data Extractor
 * Extracts test data from k6 output and saves it for Playwright consumption
 */

const fs = require('fs');
const path = require('path');

function extractTestDataFromOutput(k6Output) {
  const lines = k6Output.split('\n');
  let capturing = false;
  let testDataLines = [];
  
  for (const line of lines) {
    if (line.includes('=== TEST DATA START ===')) {
      capturing = true;
      continue;
    }
    
    if (line.includes('=== TEST DATA END ===')) {
      capturing = false;
      break;
    }
    
    if (capturing) {
      testDataLines.push(line);
    }
  }
  
  if (testDataLines.length === 0) {
    throw new Error('No test data found in k6 output');
  }
  
  try {
    const testDataJson = testDataLines.join('\n');
    return JSON.parse(testDataJson);
  } catch (error) {
    throw new Error(`Failed to parse test data JSON: ${error.message}`);
  }
}

function saveTestData(testData, outputDir = 'shared-test-data') {
  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `test-data-${testData.testRun.id}-${timestamp}.json`;
  const filepath = path.join(outputDir, filename);
  
  // Save the full test data
  fs.writeFileSync(filepath, JSON.stringify(testData, null, 2));
  
  // Also save a "latest" version for easy access
  const latestPath = path.join(outputDir, 'latest-test-data.json');
  fs.writeFileSync(latestPath, JSON.stringify(testData, null, 2));
  
  // Create a summary file for quick reference
  const summary = {
    testRunId: testData.testRun.id,
    timestamp: testData.testRun.timestamp,
    environment: testData.testRun.environment,
    userId: testData.userSession.userId,
    userName: testData.userSession.userName,
    email: testData.userSession.email,
    totalTransactions: testData.wallet.transactions.length,
    totalGameRounds: testData.gaming.rounds.length,
    totalMParticleEvents: testData.mParticleEvents.length,
    policiesAccepted: testData.policies.length,
    dataFile: filename
  };
  
  const summaryPath = path.join(outputDir, 'latest-test-summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  
  return {
    dataFile: filepath,
    latestFile: latestPath,
    summaryFile: summaryPath,
    summary: summary
  };
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.error('Usage: node extract-test-data.js <k6-output-file> [output-directory]');
    console.error('   or: echo "k6 output" | node extract-test-data.js - [output-directory]');
    process.exit(1);
  }
  
  const inputFile = args[0];
  const outputDir = args[1] || 'shared-test-data';
  
  try {
    let k6Output;
    
    if (inputFile === '-') {
      // Read from stdin
      k6Output = fs.readFileSync(0, 'utf8');
    } else {
      // Read from file
      k6Output = fs.readFileSync(inputFile, 'utf8');
    }
    
    console.log('🔍 Extracting test data from k6 output...');
    const testData = extractTestDataFromOutput(k6Output);
    
    console.log('💾 Saving test data...');
    const result = saveTestData(testData, outputDir);
    
    console.log('✅ Test data extraction completed successfully!');
    console.log(`📁 Data file: ${result.dataFile}`);
    console.log(`🔗 Latest file: ${result.latestFile}`);
    console.log(`📋 Summary file: ${result.summaryFile}`);
    console.log('\n📊 Test Summary:');
    console.log(`   Test Run ID: ${result.summary.testRunId}`);
    console.log(`   User: ${result.summary.userName} (${result.summary.email})`);
    console.log(`   Transactions: ${result.summary.totalTransactions}`);
    console.log(`   Game Rounds: ${result.summary.totalGameRounds}`);
    console.log(`   Expected mParticle Events: ${result.summary.totalMParticleEvents}`);
    console.log(`   Policies Accepted: ${result.summary.policiesAccepted}`);
    
  } catch (error) {
    console.error('❌ Error extracting test data:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  extractTestDataFromOutput,
  saveTestData
};
