@echo off
REM PAAS End-to-End Test Runner with Data Generation for Windows
REM This script runs k6 to generate test data, then validates it with Play<PERSON>

setlocal enabledelayedexpansion

echo 🚀 Starting PAAS End-to-End Validation Pipeline
echo ==============================================

REM Check prerequisites
echo 🔍 Checking prerequisites...

k6 version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ k6 is not installed. Please install k6 first:
    echo    https://k6.io/docs/getting-started/installation/
    exit /b 1
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Parse command line arguments
set ENVIRONMENT=qa
set HEADED=
set CLEAN_DATA=

:parse_args
if "%~1"=="" goto args_done
if "%~1"=="--env" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--headed" (
    set HEADED=--headed
    shift
    goto parse_args
)
if "%~1"=="--clean" (
    set CLEAN_DATA=true
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help
echo Unknown option %~1
exit /b 1

:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   --env ENV     Environment to test (default: qa)
echo   --headed      Run Playwright tests in headed mode
echo   --clean       Clean test data before running
echo   -h, --help    Show this help message
exit /b 0

:args_done

echo 🎯 Configuration:
echo    Environment: %ENVIRONMENT%
echo    Headed mode: %HEADED%
echo    Clean data: %CLEAN_DATA%
echo.

REM Clean test data if requested
if "%CLEAN_DATA%"=="true" (
    echo 🧹 Cleaning previous test data...
    if exist "shared-test-data\*.json" del /q "shared-test-data\*.json" >nul 2>&1
    echo ✅ Test data cleaned
    echo.
)

REM Ensure directories exist
if not exist "shared-test-data" mkdir "shared-test-data"
if not exist "k6-tests\results" mkdir "k6-tests\results"

REM Install Playwright dependencies if needed
echo 📦 Checking Playwright dependencies...
cd "PAAS End to End Validation"

if not exist "node_modules" (
    echo 📦 Installing Playwright dependencies...
    npm install
    npx playwright install
)

cd ..

REM Step 1: Run k6 to generate test data
echo ⚡ Step 1: Running k6 performance test to generate test data...
echo Environment: %ENVIRONMENT%

for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%-%HH%%Min%%Sec%"

set K6_OUTPUT_FILE=k6-tests\results\k6-output-%timestamp%.log

if exist "k6-tests\environments\%ENVIRONMENT%.env" (
    echo 📋 Using environment file: k6-tests\environments\%ENVIRONMENT%.env
    k6 run --env-file "k6-tests\environments\%ENVIRONMENT%.env" k6-tests\scripts\paaswallet.js > "%K6_OUTPUT_FILE%" 2>&1
) else (
    echo ⚠️  Warning: Environment file k6-tests\environments\%ENVIRONMENT%.env not found
    echo    Using default environment variables
    k6 run k6-tests\scripts\paaswallet.js > "%K6_OUTPUT_FILE%" 2>&1
)

if %errorlevel% neq 0 (
    echo ❌ k6 test failed with exit code %errorlevel%
    echo 📄 Check the output file: %K6_OUTPUT_FILE%
    exit /b 1
)

echo ✅ k6 test completed successfully
echo.

REM Step 2: Extract test data from k6 output
echo 📊 Step 2: Extracting test data from k6 output...

node scripts\extract-test-data.js "%K6_OUTPUT_FILE%" shared-test-data

if %errorlevel% neq 0 (
    echo ❌ Failed to extract test data from k6 output
    exit /b 1
)

echo ✅ Test data extracted successfully
echo.

REM Step 3: Validate test data exists
echo 🔍 Step 3: Validating test data...

if not exist "shared-test-data\latest-test-data.json" (
    echo ❌ Test data file not found: shared-test-data\latest-test-data.json
    exit /b 1
)

REM Show test data summary
if exist "shared-test-data\latest-test-summary.json" (
    echo 📋 Test Data Summary:
    type "shared-test-data\latest-test-summary.json"
    echo.
)

echo ✅ Test data validation passed
echo.

REM Step 4: Run Playwright tests to validate mParticle events
echo 🎭 Step 4: Running Playwright tests to validate mParticle events...

cd "PAAS End to End Validation"

if "%HEADED%"=="--headed" (
    echo 🖥️  Running in headed mode...
    npm run test:mparticle:headed
) else (
    echo 🤖 Running in headless mode...
    npm run test:mparticle
)

set PLAYWRIGHT_EXIT_CODE=%errorlevel%

cd ..

if %PLAYWRIGHT_EXIT_CODE% neq 0 (
    echo ❌ Playwright validation tests failed with exit code %PLAYWRIGHT_EXIT_CODE%
    exit /b 1
)

echo ✅ Playwright validation tests completed successfully
echo.

REM Step 5: Generate final report
echo 📊 Step 5: Generating final validation report...

set REPORT_FILE=shared-test-data\validation-report-%timestamp%.json

echo { > "%REPORT_FILE%"
echo   "testRun": { >> "%REPORT_FILE%"
echo     "timestamp": "%date:~10,4%-%date:~4,2%-%date:~7,2%T%time:~0,2%:%time:~3,2%:%time:~6,2%Z", >> "%REPORT_FILE%"
echo     "environment": "%ENVIRONMENT%", >> "%REPORT_FILE%"
echo     "k6OutputFile": "%K6_OUTPUT_FILE%", >> "%REPORT_FILE%"
echo     "playwrightExitCode": %PLAYWRIGHT_EXIT_CODE% >> "%REPORT_FILE%"
echo   }, >> "%REPORT_FILE%"
echo   "status": "PASSED", >> "%REPORT_FILE%"
echo   "summary": "End-to-end validation completed successfully" >> "%REPORT_FILE%"
echo } >> "%REPORT_FILE%"

echo ✅ Validation report generated: %REPORT_FILE%
echo.

echo 🎉 End-to-End Validation Pipeline Completed Successfully!
echo =======================================================
echo.
echo 📊 Results Summary:
echo    ✅ k6 performance test: PASSED
echo    ✅ Test data generation: PASSED
echo    ✅ mParticle validation: PASSED
echo.
echo 📁 Generated Files:
echo    📄 k6 output: %K6_OUTPUT_FILE%
echo    📄 Test data: shared-test-data\latest-test-data.json
echo    📄 Test summary: shared-test-data\latest-test-summary.json
echo    📄 Validation report: %REPORT_FILE%
echo    📄 Playwright report: PAAS End to End Validation\playwright-report\
echo.
echo 🔍 To view Playwright report:
echo    cd "PAAS End to End Validation" ^&^& npm run report
