# PAAS Validation Project

This project provides comprehensive end-to-end validation for PAAS (Platform as a Service) systems using both functional testing with Playwright and performance testing with k6.

## Project Structure

```
PAAS-validation/
├── PAAS End to End Validation/    # Playwright functional tests
│   ├── tests/                     # Test specifications
│   ├── playwright.config.ts       # Playwright configuration
│   └── package.json              # Node.js dependencies and scripts
├── k6-tests/                      # k6 performance tests
│   ├── scripts/                   # k6 test scripts
│   ├── libs/                      # Supporting libraries
│   ├── configs/                   # Test configurations
│   └── environments/              # Environment variables
└── README.md                      # This file
```

## Getting Started

### Prerequisites

1. **Node.js** (v18 or higher) - for Playwright tests
2. **k6** - for performance tests
   - Install from: https://k6.io/docs/getting-started/installation/
3. **Git** - for version control

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd PAAS-validation
   ```

2. Install Playwright dependencies:
   ```bash
   cd "PAAS End to End Validation"
   npm install
   npx playwright install
   ```

3. Set up k6 environment variables:
   ```bash
   cp k6-tests/environments/.env.example k6-tests/environments/qa.env
   # Edit qa.env with your actual credentials and endpoints
   ```

## Running Tests

### Playwright (Functional Tests)

```bash
cd "PAAS End to End Validation"

# Run all tests
npm run test

# Run tests in headed mode (visible browser)
npm run test:headed

# Run tests with UI mode
npm run test:ui

# Show test report
npm run report
```

### k6 (Performance Tests)

```bash
# From the root directory

# Run smoke test (1 user, 1 minute)
npm run k6:smoke

# Run load test (10 users, 5 minutes)
npm run k6:load

# Run stress test (up to 50 users)
npm run k6:stress

# Run with QA environment variables
npm run k6:qa

# Run basic wallet test
npm run k6:wallet
```

### Combined Testing

```bash
# Run both functional and performance tests
npm run test:all
```

## Test Scenarios

### Functional Tests (Playwright)

- **Login Flow**: Tests authentication and user login
- **Navigation**: Validates UI navigation and page interactions
- **Data Validation**: Ensures proper data handling and display

### Performance Tests (k6)

- **PAAS Wallet Test**: Complete wallet functionality including:
  - Authentication token generation
  - User login and policy acceptance
  - Wallet balance checks
  - Deposit operations
  - Game session management
  - Wager and result processing
  - User logout

## Configuration

### k6 Test Configurations

- **Smoke Test**: 1 user for 1 minute - basic functionality validation
- **Load Test**: 10 users for 5 minutes - normal load simulation
- **Stress Test**: Up to 50 users - stress testing limits

### Environment Variables

Update `k6-tests/environments/qa.env` with your environment-specific values:

```env
EMAIL=<EMAIL>
DGP_PASSWORD=your-password
PORTAL_AUTH_CLIENT_ID=your-client-id
PORTAL_CLIENT_SECRET=your-client-secret
DGP_URL=https://your-api-url.com/api/v1
DOMAIN=your-domain.com
```

## CI/CD Integration

The project includes GitHub Actions workflow for automated testing. Tests run on:
- Push to main/master branches
- Pull requests to main/master branches

## Troubleshooting

### Common Issues

1. **k6 not found**: Ensure k6 is installed and in your PATH
2. **Authentication failures**: Verify credentials in environment files
3. **Network timeouts**: Check API endpoints and network connectivity
4. **Playwright browser issues**: Run `npx playwright install` to update browsers

### Getting Help

- Check test logs for detailed error messages
- Verify environment configuration
- Ensure all dependencies are installed
- Review API endpoint availability

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

# Contribute
TODO: Explain how other users and developers can contribute to make your code better. 

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)