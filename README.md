# PAAS Validation Project

This project provides comprehensive end-to-end validation for PAAS (Platform as a Service) systems using both functional testing with Playwright and performance testing with k6.

## Project Structure

```
PAAS-validation/
├── PAAS End to End Validation/    # Playwright functional tests
│   ├── tests/                     # Test specifications
│   ├── playwright.config.ts       # Playwright configuration
│   └── package.json              # Node.js dependencies and scripts
├── k6-tests/                      # k6 performance tests
│   ├── scripts/                   # k6 test scripts
│   ├── libs/                      # Supporting libraries
│   ├── configs/                   # Test configurations
│   └── environments/              # Environment variables
└── README.md                      # This file
```

## Getting Started

### Prerequisites

1. **Node.js** (v18 or higher) - for Playwright tests
2. **k6** - for performance tests
   - Install from: https://k6.io/docs/getting-started/installation/
3. **Git** - for version control

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd PAAS-validation
   ```

2. Install Playwright dependencies:
   ```bash
   cd "PAAS End to End Validation"
   npm install
   npx playwright install
   ```

3. Set up k6 environment variables:
   ```bash
   cp k6-tests/environments/.env.example k6-tests/environments/qa.env
   # Edit qa.env with your actual credentials and endpoints
   ```

## Running Tests

### End-to-End Validation Pipeline (Recommended)

The complete validation pipeline runs k6 to generate test data, then uses Playwright to validate that data appears correctly in mParticle:

```bash
# Run complete end-to-end validation
./run-e2e-tests.sh

# Windows
run-e2e-tests.bat

# With options
./run-e2e-tests.sh --env qa --headed --clean
```

**Pipeline Steps:**
1. **k6 Performance Test** - Executes wallet operations and captures response data
2. **Data Extraction** - Extracts test data from k6 output for Playwright consumption
3. **mParticle Validation** - Playwright validates that expected events appear in mParticle
4. **Report Generation** - Creates comprehensive validation report

### Individual Test Types

#### Playwright (Functional Tests)

```bash
cd "PAAS End to End Validation"

# Run all functional tests
npm run test

# Run mParticle validation tests (requires test data from k6)
npm run test:mparticle

# Run tests in headed mode (visible browser)
npm run test:headed
npm run test:mparticle:headed

# Run tests with UI mode
npm run test:ui

# Show test report
npm run report
```

#### k6 (Performance Tests with Data Generation)

```bash
# From the root directory

# Generate test data and run smoke test
npm run k6:generate-data

# Generate test data with QA environment
npm run k6:generate-data:qa

# Run performance tests only (no data extraction)
npm run k6:smoke
npm run k6:load
npm run k6:stress
npm run k6:qa
npm run k6:wallet
```

#### Combined Testing

```bash
# Run k6 data generation + mParticle validation
npm run test:e2e
npm run test:e2e:qa

# Run traditional functional + performance tests
npm run test:all
```

## Test Scenarios

### End-to-End Validation Workflow

The integrated testing approach follows this workflow:

1. **k6 Data Generation**: Executes complete wallet operations and captures:
   - Authentication tokens and user session data
   - Wallet balance changes and transaction details
   - Gaming session data and round information
   - Policy acceptance records
   - Expected mParticle events with timestamps and attributes

2. **Data Storage**: Test data is automatically extracted and stored in JSON format:
   - `shared-test-data/latest-test-data.json` - Complete test data
   - `shared-test-data/latest-test-summary.json` - Test summary
   - `shared-test-data/validation-report-*.json` - Validation results

3. **mParticle Validation**: Playwright tests verify that:
   - Expected events appear in mParticle Live Stream
   - Event attributes match the generated test data
   - Event timing and sequence are correct
   - User session data is properly tracked

### Functional Tests (Playwright)

- **Login Flow**: Tests authentication and user login
- **Navigation**: Validates UI navigation and page interactions
- **Data Validation**: Ensures proper data handling and display
- **mParticle Integration**: Validates event tracking and data flow

### Performance Tests (k6) with Data Capture

- **PAAS Wallet Test**: Complete wallet functionality including:
  - Authentication token generation → Captures tokens for validation
  - User login and policy acceptance → Records user session data
  - Wallet balance checks → Stores balance information
  - Deposit operations → Tracks transaction details
  - Game session management → Captures gaming data
  - Wager and result processing → Records round information
  - User logout → Completes session tracking

- **mParticle Event Generation**: Each operation generates expected events:
  - Authentication events with client credentials
  - User session events with login/logout data
  - Wallet transaction events with amounts and balances
  - Gaming events with session and round details
  - Policy acceptance events with version information

## Configuration

### k6 Test Configurations

- **Smoke Test**: 1 user for 1 minute - basic functionality validation
- **Load Test**: 10 users for 5 minutes - normal load simulation
- **Stress Test**: Up to 50 users - stress testing limits

### Environment Variables

Update `k6-tests/environments/qa.env` with your environment-specific values:

```env
EMAIL=<EMAIL>
DGP_PASSWORD=your-password
PORTAL_AUTH_CLIENT_ID=your-client-id
PORTAL_CLIENT_SECRET=your-client-secret
DGP_URL=https://your-api-url.com/api/v1
DOMAIN=your-domain.com
```

## CI/CD Integration

The project includes GitHub Actions workflow for automated testing. Tests run on:
- Push to main/master branches
- Pull requests to main/master branches

## Troubleshooting

### Common Issues

1. **k6 not found**: Ensure k6 is installed and in your PATH
2. **Authentication failures**: Verify credentials in environment files
3. **Network timeouts**: Check API endpoints and network connectivity
4. **Playwright browser issues**: Run `npx playwright install` to update browsers

### Getting Help

- Check test logs for detailed error messages
- Verify environment configuration
- Ensure all dependencies are installed
- Review API endpoint availability

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

# Contribute
TODO: Explain how other users and developers can contribute to make your code better. 

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)