# QA Environment Variables for PAAS Wallet Tests
# Copy this file and update with your actual credentials

# Authentication
EMAIL=<EMAIL>
DGP_PASSWORD=Tr!!ogy2
PORTAL_AUTH_CLIENT_ID=Kff9wQQDNrlTXQmwpiziZKnUxjElDm7r
PORTAL_CLIENT_SECRET=HKyvz7ZuIHF6KBChxgvD40opjo5WNXt60UvNsrbyRqFU59gONw1WSqUY8E-A7D8H

# API Endpoints
DGP_URL=https://dgp-qaha01-api.purplecheckpoint.com/api/v1
DOMAIN=dgp-qaha01-portal.purplecheckpoint.com
AUTH_URL=https://everi-mobile-qa.us.auth0.com
AUTH_AUDIENCE=https://everi-mobile-qa.us.auth0.com/api/v2/
AUTH_SCOPE=openid profile email

# Game Configuration
GAME_UID=60af3824c4c64aa2766780c3
PLAY_MODE=R
DEVICE=DESKTOP

# Test Data
DEPOSIT_AMOUNT=2
DEPOSIT_CURRENCY=USD
WAGER_AMOUNT_CENTS=200
JACKPOT_CONTRIBUTION_MILLICENTS=2000
JACKPOT_WIN_AMOUNT_CENTS=2000
