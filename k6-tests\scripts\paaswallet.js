// Auto-generated by the postman-to-k6 converter
// PAAS Wallet Performance Test

import "../libs/shim/core.js";
import "../libs/shim/expect.js";
import "../libs/shim/urijs.js";
import "../libs/shim/jsonSchema.js";
import URI from "../libs/urijs.js";
import { group } from "k6";

export let options = { 
  maxRedirects: 4,
  // Default load test configuration
  stages: [
    { duration: '30s', target: 1 },  // Ramp up to 1 user
    { duration: '1m', target: 1 },   // Stay at 1 user
    { duration: '30s', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.1'],     // Error rate must be below 10%
  }
};

const Request = Symbol.for("request");
postman[Symbol.for("initial")]({
  options,
  environment: {
    gamePlayUN: "",
    dgptoken: "",
    dgpurl: "https://dgp-qaha01-api.purplecheckpoint.com/api/v1",
    domain: "dgp-qaha01-portal.purplecheckpoint.com",
    refresh_token: "",
    Atype1: "TNC_POLICY",
    Aversion1: "8.299999999999986",
    Atype2: "PRIVACY_POLICY",
    Aversion2: "0.1",
    Atype3: "CA_POLICY",
    Aversion3: "0.1",
    device: "DESKTOP",
    GameToken: "",
    gameSessionId: "",
    RoundId: "",
    PaymentId: "",
    JackpotContributionAmountInMillicents: "20000",
    FinishRound: "true",
    AmountInCents: "2000",
    jackpotWinAmountInCents: "20000",
    email: "<EMAIL>",
    portalauthclientid: "Kff9wQQDNrlTXQmwpiziZKnUxjElDm7r",
    portalclientsecret: "HKyvz7ZuIHF6KBChxgvD40opjo5WNXt60UvNsrbyRqFU59gONw1WSqUY8E-A7D8H",
    dgppassword: "Tr!!ogy2",
    authaudience: "https://everi-mobile-qa.us.auth0.com/api/v2/",
    authscope: "openid profile email",
    authurl: "https://everi-mobile-qa.us.auth0.com",
    logintoken: "",
    backofficetoken: "",
    dgpplayerid: "",
    gameaccesstoken: ""
  }
});

export default function() {
  
  postman[Request]({
    name: "Vi Auth Token Generation",
    id: "98385eea-e9ea-46e1-affe-4754857b80d8",
    method: "POST",
    address: "{{authurl}}/oauth/token",
    data: JSON.stringify({
      "grant_type": "password",
      "client_id": "{{portalauthclientid}}",
      "client_secret": "{{portalclientsecret}}", 
      "password": "{{dgppassword}}",
      "username": "{{email}}",
      "audience": "{{authaudience}}",
      "scope": "{{authscope}}"
    }),
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      pm.test("Response time is less than 2000ms", function() {
        pm.expect(response.timings.duration < 2000).to.be.true;
      });

      var jsonData = pm.response.json();
      pm.environment.set("dgptoken", jsonData.access_token);
    }
  });

  postman[Request]({
    name: "Login",
    id: "ecc1628a-1289-476e-8c92-1d4c4493ab9f",
    method: "POST",
    address: "{{dgpurl}}/login/login",
    data: JSON.stringify({
      "device": "DESKTOP",
      "auth0_access_token": "{{dgptoken}}",
      "deviceId": "randomString"
    }),
    headers: {
      domain_name: "{{domain}}",
      language: "EN"
    },
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      var jsonData = pm.response.json();
      pm.environment.set("logintoken", jsonData.access_token);

      pm.test("Login response has required fields", () => {
        pm.expect(jsonData.access_token).to.be.ok;
        pm.expect(jsonData.user_name).to.be.ok;
        pm.expect(jsonData.balance).to.be.ok;
      });
    }
  });

  postman[Request]({
    name: "Latest Policies",
    id: "fc322589-8cd4-4356-97a8-38b9a55e8ae2",
    method: "GET",
    address: "{{dgpurl}}/registration/latest-policies",
    headers: {
      domain_name: "{{domain}}",
      language: "EN"
    },
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      pm.test("Response time is less than 2000ms", function() {
        pm.expect(response.timings.duration < 2000).to.be.true;
      });

      var jsonData = pm.response.json();
      if (jsonData && jsonData.length >= 3) {
        pm.environment.set("Atype1", jsonData[0].type);
        pm.environment.set("Aversion1", jsonData[0].version);
        pm.environment.set("Atype2", jsonData[1].type);
        pm.environment.set("Aversion2", jsonData[1].version);
        pm.environment.set("Atype3", jsonData[2].type);
        pm.environment.set("Aversion3", jsonData[2].version);
      }

      pm.test("Policies response is array", () => {
        pm.expect(Array.isArray(jsonData)).to.be.true;
      });
    }
  });

  postman[Request]({
    name: "Accept Policies",
    id: "11de47f2-f88c-46b5-b9c7-105942696ce8",
    method: "POST",
    address: "{{dgpurl}}/login/accept-policies",
    data: JSON.stringify({
      "device": "{{device}}",
      "policies": [
        {
          "type": "{{Atype1}}",
          "version": "{{Aversion1}}"
        },
        {
          "type": "{{Atype2}}",
          "version": "{{Aversion2}}"
        },
        {
          "type": "{{Atype3}}",
          "version": "{{Aversion3}}"
        }
      ]
    }),
    headers: {
      domain_name: "{{domain}}",
      language: "EN"
    },
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      pm.test("Response time is less than 2000ms", function() {
        pm.expect(response.timings.duration < 2000).to.be.true;
      });
    },
    auth(config, Var) {
      config.headers.Authorization = `Bearer ${pm[Var]("logintoken")}`;
    }
  });

  postman[Request]({
    name: "Check Everi Wallet Balance",
    id: "27718c69-bb3b-47be-b80e-fce63e901ffb",
    method: "GET",
    address: "{{dgpurl}}/funds/everi-wallet-balance",
    headers: {
      domain_name: "{{domain}}",
      language: "EN",
      Authorization: "Bearer {{logintoken}}"
    },
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      pm.test("Wallet balance response has required fields", () => {
        const jsonData = pm.response.json();
        pm.expect(jsonData.amount !== undefined).to.be.true;
        pm.expect(jsonData.currency).to.be.ok;
      });
    },
    auth(config, Var) {
      config.headers.Authorization = `Bearer ${pm[Var]("logintoken")}`;
    }
  });

  postman[Request]({
    name: "Deposit-from-wallet-to-wager",
    id: "c6b75209-75ab-48e5-8b99-ba1bd8a6ddb2",
    method: "POST",
    address: "{{dgpurl}}/funds/deposit-from-everi-wallet",
    data: JSON.stringify({
      "deposit": {
        "amount": 2,
        "currency": "USD"
      }
    }),
    headers: {
      domain_name: "{{domain}}"
    },
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      pm.test("Deposit response has required fields", () => {
        const jsonData = pm.response.json();
        pm.expect(jsonData.dgpBalance).to.be.ok;
        pm.expect(jsonData.everiBalance).to.be.ok;
        pm.expect(jsonData.transactionTime).to.be.ok;
      });
    },
    auth(config, Var) {
      config.headers.Authorization = `Bearer ${pm[Var]("logintoken")}`;
    }
  });

  postman[Request]({
    name: "Get the game launch url",
    id: "3f90147a-8cf6-42a4-adc7-0efece8e81ad",
    method: "GET",
    address: "{{dgpurl}}/games/game-launch-url?game_uid=60af3824c4c64aa2766780c3&play_mode=R",
    headers: {
      language: "en-US",
      domain_name: "{{domain}}",
      Accept: "application/json"
    },
    post(response) {
      var JsonData = pm.response.json();

      if (JsonData && JsonData.url) {
        var list = JsonData.url.split("&");
        var token = "";
        for (var i = 0; i < list.length; i++) {
          if (list[i].startsWith("token=")) {
            token = list[i].split("token=")[1];
            break;
          }
        }
        pm.environment.set("GameToken", token);
      }
    },
    auth(config, Var) {
      config.headers.Authorization = `Bearer ${pm[Var]("logintoken")}`;
    }
  });

  postman[Request]({
    name: "Start Session",
    id: "e9088774-4465-4eb5-aea8-ed42765a702f",
    method: "POST",
    address: "https://dgp-qa01-api.purplecheckpoint.com/everi/startSession?launchToken={{GameToken}}",
    headers: {
      language: "en-US",
      domain_name: "{{domain}}",
      Accept: "application/json",
      Authorization: "Basic dXNlcjpiZWU5ZDMyZmVhM2M2NDYzMjE4NzlhMzE2ZjYyMzVhZg=="
    },
    post(response) {
      var JsonData = pm.response.json();
      if (JsonData && JsonData.sessionId) {
        pm.environment.set("gameSessionId", JsonData.sessionId);
      }
    },
    auth(config, Var) {
      const address = new URI(config.address);
      address.username("user");
      address.password("bee9d32fea3c646321879a316f6235af");
      config.address = address.toString();
      config.options.auth = "basic";
    }
  });

  postman[Request]({
    name: "Start Wager",
    id: "75cf0352-55de-40e0-a480-8fc56dabcec4",
    method: "POST",
    address: "https://dgp-qa01-api.purplecheckpoint.com/everi/wager?sessionId={{gameSessionId}}&roundId={{RoundId}}&paymentId={{PaymentId}}&amountInCents={{AmountInCents}}&finishRound={{FinishRound}}&jackpotContributionAmountInMillicents={{JackpotContributionAmountInMillicents}}",
    headers: {
      language: "en-US",
      domain_name: "{{domain}}",
      Accept: "application/json",
      Authorization: "Basic dXNlcjpiZWU5ZDMyZmVhM2M2NDYzMjE4NzlhMzE2ZjYyMzVhZg=="
    },
    pre() {
      var roundId = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);
      var paymentId = roundId + "P1" + "R" + 1;

      pm.environment.set("RoundId", roundId);
      pm.environment.set("PaymentId", paymentId);
      pm.environment.set("JackpotContributionAmountInMillicents", 2000);
      pm.environment.set("FinishRound", false);
      pm.environment.set("AmountInCents", 200);
    },
    auth(config, Var) {
      const address = new URI(config.address);
      address.username("user");
      address.password("bee9d32fea3c646321879a316f6235af");
      config.address = address.toString();
      config.options.auth = "basic";
    }
  });

  postman[Request]({
    name: "Result",
    id: "66866319-9aa2-4d02-bdb5-c288eb0c5466",
    method: "POST",
    address: "https://dgp-qa01-api.purplecheckpoint.com/everi/result?sessionId={{gameSessionId}}&roundId={{RoundId}}&paymentId={{PaymentId}}&amountInCents={{AmountInCents}}&finishRound=true&jackpotWinAmountInCents={{jackpotWinAmountInCents}}",
    headers: {
      language: "en-US",
      domain_name: "{{domain}}",
      Accept: "application/json",
      Authorization: "Basic dXNlcjpiZWU5ZDMyZmVhM2M2NDYzMjE4NzlhMzE2ZjYyMzVhZg=="
    },
    pre() {
      pm.environment.set("jackpotWinAmountInCents", 2000);
      pm.environment.set("FinishRound", true);
      pm.environment.set("AmountInCents", 200);
      var paymentId = pm.environment.get("RoundId") + "P2" + "R" + 2;
      pm.environment.set("PaymentId", paymentId);
    },
    auth(config, Var) {
      const address = new URI(config.address);
      address.username("user");
      address.password("bee9d32fea3c646321879a316f6235af");
      config.address = address.toString();
      config.options.auth = "basic";
    }
  });

  postman[Request]({
    name: "Logout",
    id: "7b70051b-1c20-4f7d-a1f8-1f5447886ea9",
    method: "POST",
    address: "{{dgpurl}}/login/logout",
    headers: {
      domain_name: "{{domain}}",
      language: "EN"
    },
    post(response) {
      pm.test("Status code is 200", function() {
        pm.expect(response.status === 200).to.be.true;
      });

      pm.test("Response time is less than 2000ms", function() {
        pm.expect(response.timings.duration < 2000).to.be.true;
      });
    },
    auth(config, Var) {
      config.headers.Authorization = `Bearer ${pm[Var]("logintoken")}`;
    }
  });
}
