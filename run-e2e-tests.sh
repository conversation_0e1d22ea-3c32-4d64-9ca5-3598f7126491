#!/bin/bash

# PAAS End-to-End Test Runner with Data Generation
# This script runs k6 to generate test data, then validates it with Playwright

set -e

echo "🚀 Starting PAAS End-to-End Validation Pipeline"
echo "=============================================="

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command -v k6 &> /dev/null; then
    echo "❌ k6 is not installed. Please install k6 first:"
    echo "   https://k6.io/docs/getting-started/installation/"
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Parse command line arguments
ENVIRONMENT="qa"
HEADED=""
CLEAN_DATA=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --env)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --headed)
      HEADED="--headed"
      shift
      ;;
    --clean)
      CLEAN_DATA="true"
      shift
      ;;
    -h|--help)
      echo "Usage: $0 [OPTIONS]"
      echo ""
      echo "Options:"
      echo "  --env ENV     Environment to test (default: qa)"
      echo "  --headed      Run Playwright tests in headed mode"
      echo "  --clean       Clean test data before running"
      echo "  -h, --help    Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option $1"
      exit 1
      ;;
  esac
done

echo "🎯 Configuration:"
echo "   Environment: $ENVIRONMENT"
echo "   Headed mode: ${HEADED:-false}"
echo "   Clean data: ${CLEAN_DATA:-false}"
echo ""

# Clean test data if requested
if [ "$CLEAN_DATA" = "true" ]; then
    echo "🧹 Cleaning previous test data..."
    rm -rf shared-test-data/*.json 2>/dev/null || true
    echo "✅ Test data cleaned"
    echo ""
fi

# Ensure directories exist
mkdir -p shared-test-data
mkdir -p k6-tests/results

# Install Playwright dependencies if needed
echo "📦 Checking Playwright dependencies..."
cd "PAAS End to End Validation"

if [ ! -d "node_modules" ]; then
    echo "📦 Installing Playwright dependencies..."
    npm install
    npx playwright install
fi

cd ..

# Step 1: Run k6 to generate test data
echo "⚡ Step 1: Running k6 performance test to generate test data..."
echo "Environment: $ENVIRONMENT"

K6_OUTPUT_FILE="k6-tests/results/k6-output-$(date +%Y%m%d-%H%M%S).log"

if [ -f "k6-tests/environments/${ENVIRONMENT}.env" ]; then
    echo "📋 Using environment file: k6-tests/environments/${ENVIRONMENT}.env"
    k6 run --env-file "k6-tests/environments/${ENVIRONMENT}.env" k6-tests/scripts/paaswallet.js 2>&1 | tee "$K6_OUTPUT_FILE"
else
    echo "⚠️  Warning: Environment file k6-tests/environments/${ENVIRONMENT}.env not found"
    echo "   Using default environment variables"
    k6 run k6-tests/scripts/paaswallet.js 2>&1 | tee "$K6_OUTPUT_FILE"
fi

K6_EXIT_CODE=${PIPESTATUS[0]}

if [ $K6_EXIT_CODE -ne 0 ]; then
    echo "❌ k6 test failed with exit code $K6_EXIT_CODE"
    echo "📄 Check the output file: $K6_OUTPUT_FILE"
    exit 1
fi

echo "✅ k6 test completed successfully"
echo ""

# Step 2: Extract test data from k6 output
echo "📊 Step 2: Extracting test data from k6 output..."

node scripts/extract-test-data.js "$K6_OUTPUT_FILE" shared-test-data

if [ $? -ne 0 ]; then
    echo "❌ Failed to extract test data from k6 output"
    exit 1
fi

echo "✅ Test data extracted successfully"
echo ""

# Step 3: Validate test data exists
echo "🔍 Step 3: Validating test data..."

if [ ! -f "shared-test-data/latest-test-data.json" ]; then
    echo "❌ Test data file not found: shared-test-data/latest-test-data.json"
    exit 1
fi

# Show test data summary
if [ -f "shared-test-data/latest-test-summary.json" ]; then
    echo "📋 Test Data Summary:"
    cat shared-test-data/latest-test-summary.json | jq '.'
    echo ""
fi

echo "✅ Test data validation passed"
echo ""

# Step 4: Run Playwright tests to validate mParticle events
echo "🎭 Step 4: Running Playwright tests to validate mParticle events..."

cd "PAAS End to End Validation"

if [ -n "$HEADED" ]; then
    echo "🖥️  Running in headed mode..."
    npm run test:mparticle:headed
else
    echo "🤖 Running in headless mode..."
    npm run test:mparticle
fi

PLAYWRIGHT_EXIT_CODE=$?

cd ..

if [ $PLAYWRIGHT_EXIT_CODE -ne 0 ]; then
    echo "❌ Playwright validation tests failed with exit code $PLAYWRIGHT_EXIT_CODE"
    exit 1
fi

echo "✅ Playwright validation tests completed successfully"
echo ""

# Step 5: Generate final report
echo "📊 Step 5: Generating final validation report..."

REPORT_FILE="shared-test-data/validation-report-$(date +%Y%m%d-%H%M%S).json"

cat > "$REPORT_FILE" << EOF
{
  "testRun": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
    "environment": "$ENVIRONMENT",
    "k6OutputFile": "$K6_OUTPUT_FILE",
    "playwrightExitCode": $PLAYWRIGHT_EXIT_CODE
  },
  "status": "PASSED",
  "summary": "End-to-end validation completed successfully"
}
EOF

echo "✅ Validation report generated: $REPORT_FILE"
echo ""

echo "🎉 End-to-End Validation Pipeline Completed Successfully!"
echo "======================================================="
echo ""
echo "📊 Results Summary:"
echo "   ✅ k6 performance test: PASSED"
echo "   ✅ Test data generation: PASSED"
echo "   ✅ mParticle validation: PASSED"
echo ""
echo "📁 Generated Files:"
echo "   📄 k6 output: $K6_OUTPUT_FILE"
echo "   📄 Test data: shared-test-data/latest-test-data.json"
echo "   📄 Test summary: shared-test-data/latest-test-summary.json"
echo "   📄 Validation report: $REPORT_FILE"
echo "   📄 Playwright report: PAAS End to End Validation/playwright-report/"
echo ""
echo "🔍 To view Playwright report:"
echo "   cd 'PAAS End to End Validation' && npm run report"
