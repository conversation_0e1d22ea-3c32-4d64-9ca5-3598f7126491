# Example Environment Variables for PAAS Wallet Tests
# Copy this file to qa.env, staging.env, or prod.env and update with actual values

# Authentication
EMAIL=<EMAIL>
DGP_PASSWORD=your-password
PORTAL_AUTH_CLIENT_ID=your-client-id
PORTAL_CLIENT_SECRET=your-client-secret

# API Endpoints
DGP_URL=https://your-dgp-api-url.com/api/v1
DOMAIN=your-domain.com
AUTH_URL=https://your-auth-url.com
AUTH_AUDIENCE=https://your-auth-audience.com/api/v2/
AUTH_SCOPE=openid profile email

# Game Configuration
GAME_UID=your-game-uid
PLAY_MODE=R
DEVICE=DESKTOP

# Test Data
DEPOSIT_AMOUNT=2
DEPOSIT_CURRENCY=USD
WAGER_AMOUNT_CENTS=200
JACKPOT_CONTRIBUTION_MILLICENTS=2000
JACKPOT_WIN_AMOUNT_CENTS=2000
