# K6 Performance Tests

This directory contains k6 performance testing scripts for the PAAS validation project.

## Structure

```
k6-tests/
├── scripts/           # Test scripts
├── libs/             # Supporting libraries and shims
├── configs/          # Configuration files
├── environments/     # Environment-specific variables
└── results/          # Test results and reports
```

## Prerequisites

1. Install k6: https://k6.io/docs/getting-started/installation/
2. Ensure you have valid credentials and environment access

## Running Tests

```bash
# Run the PAAS wallet test
k6 run k6-tests/scripts/paaswallet.js

# Run with specific configuration
k6 run --config k6-tests/configs/load-test.json k6-tests/scripts/paaswallet.js

# Run with environment variables
k6 run --env-file k6-tests/environments/qa.env k6-tests/scripts/paaswallet.js
```

## Test Scripts

- `paaswallet.js` - Complete wallet functionality test including authentication, deposits, gaming, and logout
