{"$schema": "http://json-schema.org/draft-07/schema#", "title": "PAAS Test Data Schema", "description": "Schema for test data exchange between k6 and Playwright tests", "type": "object", "properties": {"testRun": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique test run identifier"}, "timestamp": {"type": "string", "format": "date-time", "description": "Test execution timestamp"}, "environment": {"type": "string", "description": "Test environment (qa, staging, prod)"}}, "required": ["id", "timestamp", "environment"]}, "authentication": {"type": "object", "properties": {"dgpToken": {"type": "string", "description": "DGP authentication token"}, "loginToken": {"type": "string", "description": "User login token"}, "tokenExpiry": {"type": "string", "format": "date-time", "description": "Token expiration time"}}, "required": ["dgpToken", "loginToken"]}, "userSession": {"type": "object", "properties": {"userId": {"type": "string", "description": "User identifier"}, "userName": {"type": "string", "description": "User name"}, "email": {"type": "string", "description": "User email"}, "registrationState": {"type": "string", "description": "User registration state"}}, "required": ["userId", "userName", "email"]}, "wallet": {"type": "object", "properties": {"initialBalance": {"type": "object", "properties": {"real_balance": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "promo_balance": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}}}, "everiWalletBalance": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "finalBalance": {"type": "object", "properties": {"real_balance": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "promo_balance": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}}}}}, "transactions": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["deposit", "wager", "win", "withdrawal"]}, "amount": {"type": "number"}, "currency": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "transactionId": {"type": "string"}, "status": {"type": "string"}}, "required": ["type", "amount", "currency", "timestamp"]}}, "gaming": {"type": "object", "properties": {"gameToken": {"type": "string"}, "gameSessionId": {"type": "string"}, "gameUid": {"type": "string"}, "rounds": {"type": "array", "items": {"type": "object", "properties": {"roundId": {"type": "string"}, "paymentId": {"type": "string"}, "wagerAmount": {"type": "number"}, "winAmount": {"type": "number"}, "jackpotContribution": {"type": "number"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "policies": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "version": {"type": "string"}, "accepted": {"type": "boolean"}, "timestamp": {"type": "string", "format": "date-time"}}}}, "mParticleEvents": {"type": "array", "description": "Expected events that should appear in mParticle", "items": {"type": "object", "properties": {"eventType": {"type": "string"}, "eventName": {"type": "string"}, "customAttributes": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}, "userId": {"type": "string"}}}}}, "required": ["testRun", "authentication", "userSession"]}