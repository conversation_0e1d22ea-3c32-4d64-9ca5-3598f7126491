// Data Storage Utility for k6 Tests
// Captures and stores test data for Playwright consumption

import { SharedArray } from 'k6/data';
import { check } from 'k6';

// Global test data storage
let testData = {
  testRun: {
    id: '',
    timestamp: '',
    environment: 'qa'
  },
  authentication: {},
  userSession: {},
  wallet: {
    transactions: []
  },
  gaming: {
    rounds: []
  },
  policies: [],
  mParticleEvents: []
};

// Initialize test run
export function initializeTestRun(environment = 'qa') {
  const timestamp = new Date().toISOString();
  const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  testData.testRun = {
    id: testId,
    timestamp: timestamp,
    environment: environment
  };
  
  console.log(`🚀 Test Run Initialized: ${testId}`);
  return testId;
}

// Store authentication data
export function storeAuthData(dgpToken, loginToken, tokenExpiry = null) {
  testData.authentication = {
    dgpToken: dgpToken,
    loginToken: loginToken,
    tokenExpiry: tokenExpiry || new Date(Date.now() + 3600000).toISOString() // 1 hour default
  };
  
  console.log('🔐 Authentication data stored');
}

// Store user session data
export function storeUserSession(userData) {
  testData.userSession = {
    userId: userData.user_id || userData.userId,
    userName: userData.user_name || userData.userName,
    email: userData.email,
    registrationState: userData.registration_state || userData.registrationState,
    firstName: userData.first_name || userData.firstName,
    lastName: userData.last_name || userData.lastName
  };
  
  console.log(`👤 User session stored for: ${testData.userSession.userName}`);
}

// Store wallet balance data
export function storeWalletBalance(balanceData, balanceType = 'initial') {
  if (!testData.wallet[balanceType + 'Balance']) {
    testData.wallet[balanceType + 'Balance'] = {};
  }
  
  testData.wallet[balanceType + 'Balance'] = {
    real_balance: balanceData.real_balance || balanceData,
    promo_balance: balanceData.promo_balance || { amount: 0, currency: 'USD' },
    timestamp: new Date().toISOString()
  };
  
  console.log(`💰 ${balanceType} wallet balance stored`);
}

// Store Everi wallet balance
export function storeEveriWalletBalance(amount, currency) {
  testData.wallet.everiWalletBalance = {
    amount: amount,
    currency: currency,
    timestamp: new Date().toISOString()
  };
  
  console.log(`🏦 Everi wallet balance stored: ${amount} ${currency}`);
}

// Store transaction data
export function storeTransaction(type, amount, currency, transactionId = null, status = 'completed') {
  const transaction = {
    type: type,
    amount: amount,
    currency: currency,
    timestamp: new Date().toISOString(),
    transactionId: transactionId || `txn_${Date.now()}`,
    status: status
  };
  
  testData.wallet.transactions.push(transaction);
  console.log(`💳 Transaction stored: ${type} ${amount} ${currency}`);
  
  return transaction;
}

// Store gaming data
export function storeGamingSession(gameToken, gameSessionId, gameUid) {
  testData.gaming = {
    ...testData.gaming,
    gameToken: gameToken,
    gameSessionId: gameSessionId,
    gameUid: gameUid,
    timestamp: new Date().toISOString()
  };
  
  console.log(`🎮 Gaming session stored: ${gameSessionId}`);
}

// Store game round data
export function storeGameRound(roundId, paymentId, wagerAmount, winAmount = 0, jackpotContribution = 0) {
  const round = {
    roundId: roundId,
    paymentId: paymentId,
    wagerAmount: wagerAmount,
    winAmount: winAmount,
    jackpotContribution: jackpotContribution,
    timestamp: new Date().toISOString()
  };
  
  testData.gaming.rounds.push(round);
  console.log(`🎲 Game round stored: ${roundId}`);
  
  return round;
}

// Store policy acceptance
export function storePolicyAcceptance(policies) {
  const timestamp = new Date().toISOString();
  
  policies.forEach(policy => {
    testData.policies.push({
      type: policy.type,
      version: policy.version,
      accepted: true,
      timestamp: timestamp
    });
  });
  
  console.log(`📋 ${policies.length} policies stored`);
}

// Add expected mParticle event
export function addExpectedMParticleEvent(eventType, eventName, customAttributes = {}) {
  const event = {
    eventType: eventType,
    eventName: eventName,
    customAttributes: customAttributes,
    timestamp: new Date().toISOString(),
    userId: testData.userSession.userId || testData.userSession.userName
  };
  
  testData.mParticleEvents.push(event);
  console.log(`📊 mParticle event added: ${eventName}`);
  
  return event;
}

// Get current test data
export function getTestData() {
  return JSON.parse(JSON.stringify(testData)); // Deep clone
}

// Save test data to file (for k6 cloud or local file system)
export function saveTestData() {
  const filename = `test-data-${testData.testRun.id}.json`;
  const dataString = JSON.stringify(testData, null, 2);
  
  // In k6, we can't directly write files, but we can output the data
  // The data will be captured and saved by the test runner
  console.log(`📁 Test data ready for export: ${filename}`);
  console.log('=== TEST DATA START ===');
  console.log(dataString);
  console.log('=== TEST DATA END ===');
  
  return {
    filename: filename,
    data: testData
  };
}

// Validate test data completeness
export function validateTestData() {
  const errors = [];
  
  if (!testData.testRun.id) errors.push('Missing test run ID');
  if (!testData.authentication.dgpToken) errors.push('Missing DGP token');
  if (!testData.authentication.loginToken) errors.push('Missing login token');
  if (!testData.userSession.userId) errors.push('Missing user ID');
  if (!testData.wallet.initialBalance) errors.push('Missing initial wallet balance');
  
  if (errors.length > 0) {
    console.log('❌ Test data validation failed:');
    errors.forEach(error => console.log(`   - ${error}`));
    return false;
  }
  
  console.log('✅ Test data validation passed');
  return true;
}

export { testData };
