// Postman to k6 URI.js shim library
// Simplified URI manipulation for k6

class URI {
  constructor(uri) {
    this.uri = uri || '';
    this.parseURI();
  }

  parseURI() {
    // Simple URI parsing
    const match = this.uri.match(/^(https?:\/\/)?(([^:@]+):([^@]+)@)?([^:\/]+)(:(\d+))?(\/.*)?$/);
    if (match) {
      this.protocol = match[1] || 'http://';
      this.username = match[3] || '';
      this.password = match[4] || '';
      this.hostname = match[5] || '';
      this.port = match[7] || '';
      this.path = match[8] || '';
    }
  }

  username(user) {
    if (arguments.length === 0) {
      return this.username;
    }
    this._username = user;
    return this;
  }

  password(pass) {
    if (arguments.length === 0) {
      return this.password;
    }
    this._password = pass;
    return this;
  }

  hostname(host) {
    if (arguments.length === 0) {
      return this.hostname;
    }
    this._hostname = host;
    return this;
  }

  port(p) {
    if (arguments.length === 0) {
      return this.port;
    }
    this._port = p;
    return this;
  }

  toString() {
    let result = this.protocol || 'http://';
    
    if (this._username && this._password) {
      result += `${this._username}:${this._password}@`;
    }
    
    result += this._hostname || this.hostname;
    
    if (this._port) {
      result += `:${this._port}`;
    } else if (this.port) {
      result += `:${this.port}`;
    }
    
    result += this.path;
    
    return result;
  }

  static parse(uri) {
    return new URI(uri);
  }
}

export default URI;
