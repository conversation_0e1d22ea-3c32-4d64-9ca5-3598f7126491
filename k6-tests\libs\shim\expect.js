// Postman to k6 expect shim library
import { check } from 'k6';

// Global pm object extensions for expect functionality
if (typeof global.pm === 'undefined') {
  global.pm = {};
}

// Extend pm with expect functionality
global.pm.expect = function(actual) {
  return {
    to: {
      be: {
        true: actual === true,
        false: actual === false,
        ok: !!actual,
        undefined: actual === undefined,
        null: actual === null
      },
      equal: function(expected) {
        return actual === expected;
      },
      eql: function(expected) {
        return JSON.stringify(actual) === JSON.stringify(expected);
      },
      have: {
        property: function(prop) {
          return actual && actual.hasOwnProperty(prop);
        },
        length: function(len) {
          return actual && actual.length === len;
        }
      },
      include: function(value) {
        if (Array.isArray(actual)) {
          return actual.includes(value);
        }
        if (typeof actual === 'string') {
          return actual.indexOf(value) !== -1;
        }
        return false;
      },
      match: function(regex) {
        return typeof actual === 'string' && regex.test(actual);
      }
    },
    not: {
      to: {
        be: {
          true: actual !== true,
          false: actual !== false,
          ok: !actual,
          undefined: actual !== undefined,
          null: actual !== null
        },
        equal: function(expected) {
          return actual !== expected;
        },
        include: function(value) {
          if (Array.isArray(actual)) {
            return !actual.includes(value);
          }
          if (typeof actual === 'string') {
            return actual.indexOf(value) === -1;
          }
          return true;
        }
      }
    }
  };
};

export default global.pm.expect;
