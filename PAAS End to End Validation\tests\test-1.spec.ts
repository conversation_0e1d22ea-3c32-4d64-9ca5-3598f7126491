import { test, expect } from '@playwright/test';
const { chromium } = require('playwright')

declare global {
  interface Window {
    chrome: any;
  }
}

test('test', async ({page}) => {
  const browser = await chromium.launch({
    headless: false
  })

  await page.addInitScript(() => {
    Object.defineProperty(navigator, 'webdriver', { get: () => false });
    Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });
    Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3] });
    window.chrome = { runtime: {} };
  });

  const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
  await page.goto('https://app.us2.mparticle.com/data-platform/livestream');
  await page.getByRole('textbox', { name: '<EMAIL>' }).click();
  await page.getByRole('textbox', { name: '<EMAIL>' }).focus();
  await page.keyboard.type('<EMAIL>', { delay: 100 });
  await delay(1500);
  await page.getByRole('textbox', { name: 'your password' }).click();
  await page.getByRole('textbox', { name: 'your password' }).focus();
  await page.keyboard.type('Mparticle@12345', { delay: 100 });
  await delay(1500);
  await page.getByRole('button', { name: 'Log In' }).click();
  await delay(10000);
  await page.locator('img').click();
  await page.getByText('Everi Base App').click();
  await delay(5000);
  await page.getByTestId('icon-dataPlatform-light').locator('path').click();
  await page.getByRole('link', { name: 'Live Stream' }).click();
  await page.getByRole('button', { name: 'All Inputs' }).click();
  await page.getByRole('button', { name: 'Vi - QA' }).click();
  await page.getByRole('button', { name: 'All Outputs' }).click();
  await page.getByRole('button', { name: 'Analytics - Everi Base App' }).click();
});