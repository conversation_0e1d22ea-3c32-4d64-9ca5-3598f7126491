@echo off
REM PAAS Validation Test Runner for Windows
REM This script runs both Playwright and k6 tests

echo 🚀 Starting PAAS Validation Tests
echo ==================================

REM Check if k6 is installed
k6 version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ k6 is not installed. Please install k6 first:
    echo    https://k6.io/docs/getting-started/installation/
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Run Playwright tests
echo 🎭 Running Playwright functional tests...
cd "PAAS End to End Validation"

if not exist "node_modules" (
    echo 📦 Installing Playwright dependencies...
    npm install
    npx playwright install
)

npm run test
if %errorlevel% neq 0 (
    echo ❌ Playwright tests failed
    exit /b 1
)
echo ✅ Playwright tests completed
echo.

REM Go back to root directory
cd ..

REM Run k6 smoke test
echo ⚡ Running k6 performance smoke test...

REM Check if environment file exists
if not exist "k6-tests\environments\qa.env" (
    echo ⚠️  Warning: qa.env not found. Using default environment variables.
    echo    Copy k6-tests\environments\.env.example to k6-tests\environments\qa.env
    echo    and update with your actual credentials for better testing.
    k6 run k6-tests\scripts\paaswallet.js
) else (
    k6 run --env-file k6-tests\environments\qa.env k6-tests\scripts\paaswallet.js
)

if %errorlevel% neq 0 (
    echo ❌ k6 tests failed
    exit /b 1
)
echo ✅ k6 smoke test completed
echo.

echo 🎉 All tests completed successfully!
echo 📊 Check the reports:
echo    - Playwright: PAAS End to End Validation\playwright-report\
echo    - k6: Console output above
