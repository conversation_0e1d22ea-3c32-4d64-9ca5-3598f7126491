/**
 * Test Data Helper Utilities
 * Functions to help with test data validation and comparison
 */

import * as fs from 'fs';
import * as path from 'path';

export interface TestData {
  testRun: {
    id: string;
    timestamp: string;
    environment: string;
  };
  authentication: {
    dgpToken: string;
    loginToken: string;
    tokenExpiry?: string;
  };
  userSession: {
    userId: string;
    userName: string;
    email: string;
    registrationState: string;
    firstName?: string;
    lastName?: string;
  };
  wallet: {
    initialBalance?: any;
    everiWalletBalance?: any;
    finalBalance?: any;
    transactions: Array<{
      type: string;
      amount: number;
      currency: string;
      timestamp: string;
      transactionId: string;
      status: string;
    }>;
  };
  gaming: {
    gameToken?: string;
    gameSessionId?: string;
    gameUid?: string;
    rounds: Array<{
      roundId: string;
      paymentId: string;
      wagerAmount: number;
      winAmount: number;
      jackpotContribution: number;
      timestamp: string;
    }>;
  };
  policies: Array<{
    type: string;
    version: string;
    accepted: boolean;
    timestamp: string;
  }>;
  mParticleEvents: Array<{
    eventType: string;
    eventName: string;
    customAttributes: any;
    timestamp: string;
    userId: string;
  }>;
}

export interface TestSummary {
  testRunId: string;
  timestamp: string;
  environment: string;
  userId: string;
  userName: string;
  email: string;
  totalTransactions: number;
  totalGameRounds: number;
  totalMParticleEvents: number;
  policiesAccepted: number;
  dataFile: string;
}

/**
 * Load the latest test data generated by k6
 */
export function loadLatestTestData(): TestData | null {
  const testDataPath = path.join(__dirname, '../../shared-test-data/latest-test-data.json');
  
  if (!fs.existsSync(testDataPath)) {
    console.log('⚠️ No test data found. Run k6 tests first to generate test data.');
    return null;
  }
  
  try {
    const testDataContent = fs.readFileSync(testDataPath, 'utf8');
    return JSON.parse(testDataContent);
  } catch (error) {
    console.error('❌ Failed to load test data:', error);
    return null;
  }
}

/**
 * Load test summary
 */
export function loadTestSummary(): TestSummary | null {
  const summaryPath = path.join(__dirname, '../../shared-test-data/latest-test-summary.json');
  
  if (!fs.existsSync(summaryPath)) {
    return null;
  }
  
  try {
    const summaryContent = fs.readFileSync(summaryPath, 'utf8');
    return JSON.parse(summaryContent);
  } catch (error) {
    console.error('❌ Failed to load test summary:', error);
    return null;
  }
}

/**
 * Get events by type
 */
export function getEventsByType(testData: TestData, eventType: string): any[] {
  return testData.mParticleEvents.filter(event => event.eventType === eventType);
}

/**
 * Get events by name
 */
export function getEventsByName(testData: TestData, eventName: string): any[] {
  return testData.mParticleEvents.filter(event => event.eventName === eventName);
}

/**
 * Get events containing specific attributes
 */
export function getEventsWithAttribute(testData: TestData, attributeKey: string, attributeValue?: any): any[] {
  return testData.mParticleEvents.filter(event => {
    if (!event.customAttributes || !event.customAttributes[attributeKey]) {
      return false;
    }
    
    if (attributeValue !== undefined) {
      return event.customAttributes[attributeKey] === attributeValue;
    }
    
    return true;
  });
}

/**
 * Validate event sequence
 */
export function validateEventSequence(testData: TestData, expectedSequence: string[]): boolean {
  const sortedEvents = testData.mParticleEvents.sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
  
  let sequenceIndex = 0;
  
  for (const event of sortedEvents) {
    if (sequenceIndex < expectedSequence.length && 
        event.eventName === expectedSequence[sequenceIndex]) {
      sequenceIndex++;
    }
  }
  
  return sequenceIndex === expectedSequence.length;
}

/**
 * Get transaction summary
 */
export function getTransactionSummary(testData: TestData) {
  const transactions = testData.wallet.transactions;
  
  return {
    total: transactions.length,
    byType: transactions.reduce((acc, txn) => {
      acc[txn.type] = (acc[txn.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    totalAmount: transactions.reduce((sum, txn) => sum + txn.amount, 0),
    currencies: [...new Set(transactions.map(txn => txn.currency))]
  };
}

/**
 * Get gaming summary
 */
export function getGamingSummary(testData: TestData) {
  const rounds = testData.gaming.rounds;
  
  return {
    totalRounds: rounds.length,
    totalWagered: rounds.reduce((sum, round) => sum + round.wagerAmount, 0),
    totalWon: rounds.reduce((sum, round) => sum + round.winAmount, 0),
    totalJackpotContribution: rounds.reduce((sum, round) => sum + round.jackpotContribution, 0),
    gameSessionId: testData.gaming.gameSessionId,
    gameToken: testData.gaming.gameToken
  };
}

/**
 * Validate wallet balance consistency
 */
export function validateWalletBalanceConsistency(testData: TestData): boolean {
  const { initialBalance, finalBalance, transactions } = testData.wallet;
  
  if (!initialBalance || !finalBalance) {
    return false;
  }
  
  // Calculate expected balance based on transactions
  let expectedBalance = initialBalance.real_balance.amount;
  
  for (const transaction of transactions) {
    if (transaction.type === 'deposit') {
      expectedBalance += transaction.amount;
    } else if (transaction.type === 'wager') {
      expectedBalance -= transaction.amount;
    } else if (transaction.type === 'win') {
      expectedBalance += transaction.amount;
    }
  }
  
  // Allow for small floating point differences
  const difference = Math.abs(expectedBalance - finalBalance.real_balance.amount);
  return difference < 0.01;
}

/**
 * Generate validation report
 */
export function generateValidationReport(testData: TestData) {
  const transactionSummary = getTransactionSummary(testData);
  const gamingSummary = getGamingSummary(testData);
  const balanceConsistent = validateWalletBalanceConsistency(testData);
  
  return {
    testRun: testData.testRun,
    userSession: testData.userSession,
    transactions: transactionSummary,
    gaming: gamingSummary,
    policies: {
      total: testData.policies.length,
      accepted: testData.policies.filter(p => p.accepted).length
    },
    mParticleEvents: {
      total: testData.mParticleEvents.length,
      byType: testData.mParticleEvents.reduce((acc, event) => {
        acc[event.eventType] = (acc[event.eventType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byName: testData.mParticleEvents.reduce((acc, event) => {
        acc[event.eventName] = (acc[event.eventName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    },
    validation: {
      balanceConsistent,
      hasAuthenticationEvents: getEventsByName(testData, 'Auth Token Generated').length > 0,
      hasLoginEvents: getEventsByName(testData, 'Login Success').length > 0,
      hasWalletEvents: getEventsWithAttribute(testData, 'balance_amount').length > 0,
      hasGamingEvents: getEventsByName(testData, 'Game Session Started').length > 0
    }
  };
}

/**
 * Wait for test data to be available
 */
export async function waitForTestData(maxWaitMs = 60000, checkIntervalMs = 1000): Promise<TestData | null> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitMs) {
    const testData = loadLatestTestData();
    if (testData) {
      return testData;
    }
    
    await new Promise(resolve => setTimeout(resolve, checkIntervalMs));
  }
  
  return null;
}
